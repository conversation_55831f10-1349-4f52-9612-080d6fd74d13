import { createTRPCRouter, publicProcedure } from "../lib/trpc";
import { accountsRouter } from "./accounts";
import { benjiRouter } from "./benji";
import { billingRouter } from "./billing";
import { cryptoRouter } from "./crypto";
import { mentionsRouter } from "./mentions";
import { notepadRouter } from "./notepad";
import { telegramRouter } from "./telegram";
import { twitterRouter } from "./twitter";
import { userRouter } from "./user";

export const appRouter = createTRPCRouter({
  healthCheck: publicProcedure.query(() => {
    return "OK";
  }),
  benji: benji<PERSON>outer,
  user: userRouter,
  accounts: accountsRouter,
  mentions: mentionsRouter,
  twitter: twitterRouter,
  billing: billingRouter,
  crypto: cryptoRouter,
  telegram: telegramRouter,
  notepad: notepadRouter,
});

export type AppRouter = typeof appRouter;
