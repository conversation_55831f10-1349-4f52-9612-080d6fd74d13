#!/usr/bin/env node

/**
 * Backend Infrastructure Investigation Script
 * 
 * Comprehensive testing of backend systems for Telegram bot
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 BuddyChip Backend Infrastructure Investigation\n');

// Check working directory
const cwd = process.cwd();
console.log(`📂 Working Directory: ${cwd}`);
console.log(`📂 Web App Directory: ${path.join(cwd, 'apps/web')}`);

// Change to web app directory
process.chdir(path.join(cwd, 'apps/web'));

const investigations = [
  {
    name: 'Environment Variables',
    test: () => {
      console.log('🔧 Checking environment variables...');
      
      const envFile = path.join(process.cwd(), '.env');
      const envLocalFile = path.join(process.cwd(), '.env.local');
      
      console.log(`  .env exists: ${fs.existsSync(envFile)}`);
      console.log(`  .env.local exists: ${fs.existsSync(envLocalFile)}`);
      
      const requiredVars = [
        'DATABASE_URL',
        'DIRECT_URL', 
        'CLERK_SECRET_KEY',
        'OPENROUTER_API_KEY',
        'OPENAI_API_KEY',
        'TELEGRAM_BOT_TOKEN'
      ];
      
      console.log('  Required variables:');
      requiredVars.forEach(varName => {
        const exists = !!process.env[varName];
        const masked = exists ? 
          `${process.env[varName].substring(0, 10)}...` : 
          'NOT SET';
        console.log(`    ${varName}: ${exists ? '✅' : '❌'} ${masked}`);
      });
    }
  },
  
  {
    name: 'Database Connection',
    test: async () => {
      console.log('🗄️ Testing database connection...');
      
      try {
        // Try to start the dev server briefly to test DB connection
        console.log('  Attempting database connection test...');
        
        const dbTest = execSync('curl -s http://localhost:3000/api/test-db || echo "Server not running"', { 
          encoding: 'utf8',
          timeout: 5000 
        });
        
        console.log(`  Database test result: ${dbTest.trim()}`);
        
      } catch (error) {
        console.log(`  ❌ Database connection test failed: ${error.message}`);
      }
    }
  },
  
  {
    name: 'Prisma Configuration',
    test: () => {
      console.log('🔧 Checking Prisma configuration...');
      
      const schemaPath = path.join(process.cwd(), 'prisma/schema/schema.prisma');
      const generatedPath = path.join(process.cwd(), 'prisma/generated');
      const configPath = path.join(process.cwd(), 'prisma.config.ts');
      
      console.log(`  Schema file exists: ${fs.existsSync(schemaPath) ? '✅' : '❌'}`);
      console.log(`  Generated client exists: ${fs.existsSync(generatedPath) ? '✅' : '❌'}`);
      console.log(`  Prisma config exists: ${fs.existsSync(configPath) ? '✅' : '❌'}`);
      
      try {
        const result = execSync('npx prisma validate --schema ./prisma/schema/schema.prisma', { 
          encoding: 'utf8' 
        });
        console.log('  ✅ Prisma schema is valid');
      } catch (error) {
        console.log(`  ❌ Prisma schema validation failed: ${error.message}`);
      }
    }
  },
  
  {
    name: 'tRPC Routes',
    test: () => {
      console.log('🛠️ Checking tRPC routes...');
      
      const routersPath = path.join(process.cwd(), 'src/routers');
      const telegramRouterPath = path.join(routersPath, 'telegram.ts');
      const indexRouterPath = path.join(routersPath, 'index.ts');
      
      console.log(`  Routers directory exists: ${fs.existsSync(routersPath) ? '✅' : '❌'}`);
      console.log(`  Telegram router exists: ${fs.existsSync(telegramRouterPath) ? '✅' : '❌'}`);
      console.log(`  Index router exists: ${fs.existsSync(indexRouterPath) ? '✅' : '❌'}`);
      
      if (fs.existsSync(indexRouterPath)) {
        const indexContent = fs.readFileSync(indexRouterPath, 'utf8');
        const hasTelegramRouter = indexContent.includes('telegramRouter');
        console.log(`  Telegram router imported: ${hasTelegramRouter ? '✅' : '❌'}`);
      }
    }
  },
  
  {
    name: 'Telegram Integration Files',
    test: () => {
      console.log('📱 Checking Telegram integration files...');
      
      const libPath = path.join(process.cwd(), 'src/lib');
      const telegramFiles = [
        'telegram-auth.ts',
        'telegram-bot.ts', 
        'telegram-benji-agent.ts',
        'telegram-rate-limiting.ts',
        'telegram-security.ts',
        'telegram-validation.ts'
      ];
      
      telegramFiles.forEach(file => {
        const filePath = path.join(libPath, file);
        console.log(`  ${file}: ${fs.existsSync(filePath) ? '✅' : '❌'}`);
      });
      
      // Check webhook route
      const webhookPath = path.join(process.cwd(), 'src/app/api/telegram/webhook/route.ts');
      console.log(`  Webhook route: ${fs.existsSync(webhookPath) ? '✅' : '❌'}`);
    }
  },
  
  {
    name: 'AI Services',
    test: () => {
      console.log('🤖 Checking AI services...');
      
      const libPath = path.join(process.cwd(), 'src/lib');
      const aiFiles = [
        'benji-agent.ts',
        'ai-providers.ts',
        'mem0-service.ts'
      ];
      
      aiFiles.forEach(file => {
        const filePath = path.join(libPath, file);
        console.log(`  ${file}: ${fs.existsSync(filePath) ? '✅' : '❌'}`);
      });
      
      // Check tools directory
      const toolsPath = path.join(libPath, 'tools');
      if (fs.existsSync(toolsPath)) {
        const tools = fs.readdirSync(toolsPath);
        console.log(`  AI tools available: ${tools.length} files`);
        tools.forEach(tool => {
          console.log(`    - ${tool}`);
        });
      } else {
        console.log('  ❌ Tools directory not found');
      }
    }
  },
  
  {
    name: 'Package Dependencies',
    test: () => {
      console.log('📦 Checking package dependencies...');
      
      const packagePath = path.join(process.cwd(), 'package.json');
      if (fs.existsSync(packagePath)) {
        const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        
        const telegramDeps = [
          'node-telegram-bot-api',
          '@trpc/server',
          'prisma',
          '@prisma/client'
        ];
        
        console.log('  Required dependencies:');
        telegramDeps.forEach(dep => {
          const hasDep = (pkg.dependencies && pkg.dependencies[dep]) || 
                       (pkg.devDependencies && pkg.devDependencies[dep]);
          console.log(`    ${dep}: ${hasDep ? '✅' : '❌'}`);
        });
      } else {
        console.log('  ❌ package.json not found');
      }
    }
  },
  
  {
    name: 'TypeScript Configuration',
    test: () => {
      console.log('📝 Checking TypeScript configuration...');
      
      const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');
      const nextConfigPath = path.join(process.cwd(), 'next.config.ts');
      
      console.log(`  tsconfig.json exists: ${fs.existsSync(tsconfigPath) ? '✅' : '❌'}`);
      console.log(`  next.config.ts exists: ${fs.existsSync(nextConfigPath) ? '✅' : '❌'}`);
      
      try {
        execSync('npx tsc --noEmit', { encoding: 'utf8' });
        console.log('  ✅ TypeScript compilation successful');
      } catch (error) {
        console.log(`  ⚠️ TypeScript compilation issues detected`);
      }
    }
  },
  
  {
    name: 'Build System',
    test: () => {
      console.log('🏗️ Checking build system...');
      
      try {
        const scripts = execSync('npm run', { encoding: 'utf8' });
        const availableScripts = scripts.match(/^\s+(\w+)/gm)?.map(s => s.trim()) || [];
        
        console.log('  Available scripts:');
        availableScripts.forEach(script => {
          console.log(`    - ${script}`);
        });
        
        const importantScripts = ['dev', 'build', 'start', 'test'];
        importantScripts.forEach(script => {
          const hasScript = availableScripts.includes(script);
          console.log(`  ${script}: ${hasScript ? '✅' : '❌'}`);
        });
        
      } catch (error) {
        console.log(`  ❌ Could not check scripts: ${error.message}`);
      }
    }
  }
];

async function runInvestigation() {
  console.log(`\n🚀 Starting investigation of ${investigations.length} components...\n`);
  
  for (let i = 0; i < investigations.length; i++) {
    const investigation = investigations[i];
    
    console.log(`\n${'='.repeat(60)}`);
    console.log(`${i + 1}. ${investigation.name}`);
    console.log(`${'='.repeat(60)}`);
    
    try {
      await investigation.test();
      console.log(`✅ ${investigation.name} check completed`);
    } catch (error) {
      console.log(`❌ ${investigation.name} check failed: ${error.message}`);
    }
  }
  
  console.log(`\n${'='.repeat(60)}`);
  console.log('🏁 Investigation Complete');
  console.log(`${'='.repeat(60)}\n`);
  
  console.log('📋 Summary of findings:');
  console.log('- Check each section above for specific issues');
  console.log('- ✅ indicates working components');
  console.log('- ❌ indicates issues that need attention');
  console.log('- ⚠️ indicates warnings or potential issues');
  
  console.log('\n💡 Next steps:');
  console.log('1. Fix any ❌ issues found above');
  console.log('2. Ensure environment variables are properly set');
  console.log('3. Test database connectivity');
  console.log('4. Verify Telegram webhook configuration');
  console.log('5. Run integration tests');
}

// Run the investigation
runInvestigation().catch(error => {
  console.error('💥 Investigation failed:', error);
  process.exit(1);
});